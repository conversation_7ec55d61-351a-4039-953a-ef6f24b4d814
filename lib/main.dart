import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';

import 'pages/chat_page.dart';
import 'services/xmtp_service.dart';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (context) => XmtpService(),
      child: MaterialApp(
        title: 'XMTP Chat App',
        theme: ThemeData(
          colorScheme: ColorScheme.fromSeed(seedColor: Colors.blue),
          useMaterial3: true,
        ),
        home: const HomePage(),
      ),
    );
  }
}

/// Home page for XMTP Chat App
class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  final TextEditingController _peerAddressController = TextEditingController();
  final TextEditingController _privateKeyController = TextEditingController();

  @override
  void dispose() {
    _peerAddressController.dispose();
    _privateKeyController.dispose();
    super.dispose();
  }

  /// Copy wallet address to clipboard
  Future<void> _copyAddress(String address) async {
    await Clipboard.setData(ClipboardData(text: address));
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Địa chỉ ví đã được sao chép'),
          duration: Duration(seconds: 2),
        ),
      );
    }
  }

  /// Initialize XMTP client
  Future<void> _initializeXmtp() async {
    final xmtpService = context.read<XmtpService>();
    final privateKey = _privateKeyController.text.trim();

    await xmtpService.initializeClient(
      privateKey: privateKey.isEmpty ? null : privateKey,
      useDevNetwork: true, // Use dev network for testing
    );
  }

  /// Navigate to chat page
  void _openChat() {
    final peerAddress = _peerAddressController.text.trim();
    if (peerAddress.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please enter a peer wallet address')),
      );
      return;
    }

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ChatPage(peerAddress: peerAddress),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('XMTP Chat'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: Consumer<XmtpService>(
        builder: (context, xmtpService, child) {
          return SingleChildScrollView(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // XMTP Status Card
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'XMTP Status',
                          style: Theme.of(context).textTheme.titleMedium,
                        ),
                        const SizedBox(height: 8),
                        Row(
                          children: [
                            Icon(
                              xmtpService.isInitialized
                                  ? Icons.check_circle
                                  : xmtpService.isInitializing
                                  ? Icons.hourglass_empty
                                  : Icons.error,
                              color:
                                  xmtpService.isInitialized
                                      ? Colors.green
                                      : xmtpService.isInitializing
                                      ? Colors.orange
                                      : Colors.red,
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                xmtpService.isInitialized
                                    ? 'Connected'
                                    : xmtpService.isInitializing
                                    ? 'Connecting...'
                                    : 'Not connected',
                              ),
                            ),
                          ],
                        ),
                        if (xmtpService.currentUserAddress != null) ...[
                          const SizedBox(height: 8),
                          Row(
                            children: [
                              Expanded(
                                child: Text(
                                  'Address: ${xmtpService.currentUserAddress}',
                                  style: Theme.of(context).textTheme.bodySmall,
                                ),
                              ),
                              IconButton(
                                onPressed:
                                    () => _copyAddress(
                                      xmtpService.currentUserAddress!,
                                    ),
                                icon: const Icon(Icons.copy, size: 16),
                                tooltip: 'Copy địa chỉ ví',
                                padding: EdgeInsets.zero,
                                constraints: const BoxConstraints(
                                  minWidth: 32,
                                  minHeight: 32,
                                ),
                              ),
                            ],
                          ),
                        ],
                        if (xmtpService.initializationError != null) ...[
                          const SizedBox(height: 8),
                          Text(
                            'Error: ${xmtpService.initializationError}',
                            style: TextStyle(color: Colors.red[700]),
                          ),
                        ],
                      ],
                    ),
                  ),
                ),

                const SizedBox(height: 16),

                // Private Key Input (Development Only)
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Wallet Setup (Development Only)',
                          style: Theme.of(context).textTheme.titleMedium,
                        ),
                        const SizedBox(height: 8),
                        TextField(
                          controller: _privateKeyController,
                          decoration: const InputDecoration(
                            labelText: 'Private Key (optional)',
                            hintText: 'Leave empty to generate random wallet',
                            border: OutlineInputBorder(),
                          ),
                          obscureText: true,
                        ),
                        const SizedBox(height: 8),
                        const Text(
                          'Note: In production, use WalletConnect instead of private keys',
                          style: TextStyle(fontSize: 12, color: Colors.orange),
                        ),
                      ],
                    ),
                  ),
                ),

                const SizedBox(height: 16),

                // Initialize/Disconnect Button
                if (!xmtpService.isInitialized) ...[
                  ElevatedButton(
                    onPressed:
                        xmtpService.isInitializing ? null : _initializeXmtp,
                    child:
                        xmtpService.isInitializing
                            ? const Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                SizedBox(
                                  width: 16,
                                  height: 16,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                  ),
                                ),
                                SizedBox(width: 8),
                                Text('Initializing...'),
                              ],
                            )
                            : const Text('Initialize XMTP Client'),
                  ),
                ] else ...[
                  ElevatedButton(
                    onPressed: () async {
                      await xmtpService.disconnect();
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red[100],
                      foregroundColor: Colors.red[800],
                    ),
                    child: const Text('Disconnect'),
                  ),
                ],

                const SizedBox(height: 24),

                // Chat Section
                if (xmtpService.isInitialized) ...[
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Start a Chat',
                            style: Theme.of(context).textTheme.titleMedium,
                          ),
                          const SizedBox(height: 8),
                          TextField(
                            controller: _peerAddressController,
                            decoration: const InputDecoration(
                              labelText: 'Peer Wallet Address',
                              hintText: '0x...',
                              border: OutlineInputBorder(),
                            ),
                          ),
                          const SizedBox(height: 16),
                          ElevatedButton(
                            onPressed: _openChat,
                            child: const Text('Open Chat'),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],

                // Status Section
                Card(
                  color: Colors.green[50],
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'XMTP Features Status',
                          style: Theme.of(context).textTheme.titleMedium,
                        ),
                        const SizedBox(height: 8),
                        const Text('✅ XMTP Client Integration'),
                        const Text('✅ Wallet Connection'),
                        const Text('✅ Message Sending/Receiving'),
                        const Text('✅ Real-time Message Streaming'),
                        const Text('✅ Conversation Management'),
                        const Text('✅ End-to-End Encryption'),
                        const SizedBox(height: 8),
                        Text(
                          'Future Enhancements',
                          style: Theme.of(context).textTheme.titleSmall,
                        ),
                        const Text('• WalletConnect integration'),
                        const Text('• Push notifications'),
                        const Text('• Message persistence'),
                        const Text('• Consent filtering'),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}
