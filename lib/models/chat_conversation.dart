import 'chat_message.dart';

/// Model class representing a chat conversation with another wallet address
class ChatConversation {
  final String peerAddress;
  final List<ChatMessage> messages;
  final DateTime? lastMessageAt;
  final String? lastMessageContent;

  const ChatConversation({
    required this.peerAddress,
    required this.messages,
    this.lastMessageAt,
    this.lastMessageContent,
  });

  /// Create an empty conversation with a peer address
  factory ChatConversation.empty(String peerAddress) {
    return ChatConversation(
      peerAddress: peerAddress,
      messages: [],
    );
  }

  /// Create a conversation with messages
  ChatConversation copyWith({
    String? peerAddress,
    List<ChatMessage>? messages,
    DateTime? lastMessageAt,
    String? lastMessageContent,
  }) {
    return ChatConversation(
      peerAddress: peerAddress ?? this.peerAddress,
      messages: messages ?? this.messages,
      lastMessageAt: lastMessageAt ?? this.lastMessageAt,
      lastMessageContent: lastMessageContent ?? this.lastMessageContent,
    );
  }

  /// Add a new message to the conversation
  ChatConversation addMessage(ChatMessage message) {
    final updatedMessages = [...messages, message];
    updatedMessages.sort((a, b) => a.sentAt.compareTo(b.sentAt));
    
    return copyWith(
      messages: updatedMessages,
      lastMessageAt: message.sentAt,
      lastMessageContent: message.content,
    );
  }

  /// Get the display name for the peer (shortened address)
  String get peerDisplayName {
    if (peerAddress.length <= 10) return peerAddress;
    return '${peerAddress.substring(0, 6)}...${peerAddress.substring(peerAddress.length - 4)}';
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ChatConversation &&
          runtimeType == other.runtimeType &&
          peerAddress == other.peerAddress;

  @override
  int get hashCode => peerAddress.hashCode;

  @override
  String toString() {
    return 'ChatConversation{peerAddress: $peerAddress, messageCount: ${messages.length}, lastMessageAt: $lastMessageAt}';
  }
}
