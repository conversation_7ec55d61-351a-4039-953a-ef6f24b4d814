import 'dart:async';
import 'dart:math';

import 'package:flutter/foundation.dart';
import 'package:web3dart/credentials.dart';
import 'package:web3dart/web3dart.dart';
import 'package:xmtp/xmtp.dart' as xmtp;

import '../models/chat_message.dart';

/// Service class for managing XMTP client and messaging functionality
class XmtpService extends ChangeNotifier {
  xmtp.Client? _client;
  xmtp.Api? _api;
  String? _currentUserAddress;
  bool _isInitialized = false;
  bool _isInitializing = false;
  String? _initializationError;

  // Cache for conversations to avoid redundant API calls
  final Map<String, xmtp.Conversation> _conversationCache = {};

  // Stream subscriptions for cleanup
  final List<StreamSubscription> _subscriptions = [];

  /// Getters
  bool get isInitialized => _isInitialized;
  bool get isInitializing => _isInitializing;
  String? get initializationError => _initializationError;
  String? get currentUserAddress => _currentUserAddress;
  xmtp.Client? get client => _client;

  /// Initialize XMTP client with a private key (development mode only)
  /// In production, this should be replaced with WalletConnect integration
  Future<bool> initializeClient({
    String? privateKey,
    bool useDevNetwork = true,
  }) async {
    if (_isInitialized || _isInitializing) {
      return _isInitialized;
    }

    _isInitializing = true;
    _initializationError = null;
    notifyListeners();

    try {
      // Create wallet from private key or generate random one
      final Credentials wallet;
      if (privateKey != null && privateKey.isNotEmpty) {
        wallet = EthPrivateKey.fromHex(privateKey);
      } else {
        wallet = EthPrivateKey.createRandom(Random.secure());
      }

      _currentUserAddress = wallet.address.hex;

      // Create XMTP API instance
      final host =
          useDevNetwork ? "dev.xmtp.network" : "production.xmtp.network";
      _api = xmtp.Api.create(host: host, port: 5556, isSecure: true);

      // Create proper XMTP Signer from EthPrivateKey
      final signer = _WalletSigner(wallet as EthPrivateKey);
      _client = await xmtp.Client.createFromWallet(_api!, signer);

      _isInitialized = true;
      _initializationError = null;

      debugPrint(
        'XMTP client initialized successfully for address: $_currentUserAddress',
      );
    } catch (e) {
      _initializationError =
          'Failed to initialize XMTP service: ${e.toString()}';
      debugPrint('XMTP initialization error: $_initializationError');
      _isInitialized = false;
    } finally {
      _isInitializing = false;
      notifyListeners();
    }

    return _isInitialized;
  }

  /// Disconnect from XMTP and reset the service state
  Future<void> disconnect() async {
    try {
      // Cancel all stream subscriptions
      for (final subscription in _subscriptions) {
        await subscription.cancel();
      }
      _subscriptions.clear();

      // Clear conversation cache
      _conversationCache.clear();

      // Reset state
      _client = null;
      _api = null;
      _currentUserAddress = null;
      _isInitialized = false;
      _isInitializing = false;
      _initializationError = null;

      debugPrint('XMTP client disconnected successfully');
      notifyListeners();
    } catch (e) {
      debugPrint('Error during disconnect: ${e.toString()}');
      // Reset state anyway
      _isInitialized = false;
      _isInitializing = false;
      notifyListeners();
    }
  }

  /// Get or create a conversation with another wallet address
  Future<xmtp.Conversation?> getConversation(String peerAddress) async {
    if (!_isInitialized) {
      throw Exception('XMTP service not initialized');
    }

    // Validate peer address format
    if (!_isValidEthereumAddress(peerAddress)) {
      throw ArgumentError('Invalid Ethereum address: $peerAddress');
    }

    if (_client == null) {
      throw Exception('XMTP client not initialized');
    }

    try {
      // Check cache first
      final normalizedAddress = peerAddress.toLowerCase();
      if (_conversationCache.containsKey(normalizedAddress)) {
        return _conversationCache[normalizedAddress];
      }

      // Try to find existing conversation
      final conversations = await _client!.listConversations();
      for (final convo in conversations) {
        if (convo.peer.hex.toLowerCase() == normalizedAddress) {
          _conversationCache[normalizedAddress] = convo;
          return convo;
        }
      }

      // Create new conversation if none exists
      final newConvo = await _client!.newConversation(peerAddress);
      _conversationCache[normalizedAddress] = newConvo;

      debugPrint('Created new conversation with: $peerAddress');
      return newConvo;
    } catch (e) {
      debugPrint(
        'Error getting conversation with $peerAddress: ${e.toString()}',
      );
      rethrow;
    }
  }

  /// Send a text message to a peer address
  Future<void> sendTextMessage(String peerAddress, String text) async {
    if (!_isInitialized) {
      throw Exception('XMTP service not initialized');
    }

    // Input validation
    if (text.trim().isEmpty) {
      throw ArgumentError('Message text cannot be empty');
    }

    if (!_isValidEthereumAddress(peerAddress)) {
      throw ArgumentError('Invalid Ethereum address: $peerAddress');
    }

    if (_client == null) {
      throw Exception('XMTP client not initialized');
    }

    try {
      final conversation = await getConversation(peerAddress);
      if (conversation == null) {
        throw Exception('Failed to create conversation with $peerAddress');
      }

      await _client!.sendMessage(conversation, text.trim());
      debugPrint('Message sent to $peerAddress: $text');
    } catch (e) {
      debugPrint('Error sending message to $peerAddress: ${e.toString()}');
      rethrow;
    }
  }

  /// Stream all messages from all conversations
  /// This provides real-time message updates using conversation streaming
  Stream<ChatMessage> streamAllMessages() {
    if (!_isInitialized || _client == null) {
      throw Exception('XMTP client not initialized');
    }

    // Since streamAllMessages() is not available, we'll use streamConversations()
    // and then stream messages from each conversation
    return _client!
        .streamConversations()
        .asyncExpand((conversation) {
          debugPrint('New conversation detected: ${conversation.peer.hex}');

          // Stream messages from this conversation
          return _client!.streamMessages(conversation).map((message) {
            return ChatMessage.fromXmtpMessage(
              id:
                  '${message.sentAt.millisecondsSinceEpoch}_${message.sender.hex}',
              content: message.content.toString(),
              senderAddress: message.sender.hex,
              sentAt: message.sentAt,
              currentUserAddress: _currentUserAddress!,
            );
          });
        })
        .handleError((error) {
          debugPrint('Error in message stream: ${error.toString()}');
        });
  }

  /// Stream messages from a specific conversation
  /// This provides real-time message updates for a single conversation
  Stream<ChatMessage> streamMessages(String peerAddress) async* {
    if (!_isInitialized || _client == null) {
      throw Exception('XMTP client not initialized');
    }

    try {
      final conversation = await getConversation(peerAddress);
      if (conversation == null) {
        debugPrint('No conversation found with $peerAddress');
        return;
      }

      yield* _client!.streamMessages(conversation).map((message) {
        return ChatMessage.fromXmtpMessage(
          id: '${message.sentAt.millisecondsSinceEpoch}_${message.sender.hex}',
          content: message.content.toString(),
          senderAddress: message.sender.hex,
          sentAt: message.sentAt,
          currentUserAddress: _currentUserAddress!,
        );
      });
    } catch (e) {
      debugPrint('Error streaming messages for $peerAddress: ${e.toString()}');
      rethrow;
    }
  }

  /// Check if a peer address can receive messages (has XMTP identity)
  Future<bool> canMessage(String peerAddress) async {
    if (!_isInitialized || _client == null) {
      throw Exception('XMTP client not initialized');
    }

    if (!_isValidEthereumAddress(peerAddress)) {
      return false;
    }

    try {
      // This is a placeholder - the actual XMTP SDK may have a canMessage method
      // For now, we'll try to create a conversation and catch any errors
      await getConversation(peerAddress);
      return true;
    } catch (e) {
      debugPrint('Cannot message $peerAddress: ${e.toString()}');
      return false;
    }
  }

  /// Get message history for a specific conversation
  Future<List<ChatMessage>> getMessageHistory(
    String peerAddress, {
    int? limit,
    DateTime? before,
  }) async {
    if (!_isInitialized) {
      throw Exception('XMTP service not initialized');
    }

    if (_client == null) {
      throw Exception('XMTP client not initialized');
    }

    try {
      final conversation = await getConversation(peerAddress);
      if (conversation == null) {
        return [];
      }

      final messages = await _client!.listMessages(
        conversation,
        limit: limit,
        end: before,
      );

      return messages
          .map(
            (msg) => ChatMessage.fromXmtpMessage(
              id: '${msg.sentAt.millisecondsSinceEpoch}_${msg.sender.hex}',
              content: msg.content.toString(),
              senderAddress: msg.sender.hex,
              sentAt: msg.sentAt,
              currentUserAddress: _currentUserAddress!,
            ),
          )
          .toList();
    } catch (e) {
      debugPrint(
        'Error getting message history for $peerAddress: ${e.toString()}',
      );
      rethrow;
    }
  }

  /// Validate Ethereum address format
  bool _isValidEthereumAddress(String address) {
    if (address.isEmpty) return false;

    // Basic validation: starts with 0x and has 42 characters total
    final regex = RegExp(r'^0x[a-fA-F0-9]{40}$');
    return regex.hasMatch(address);
  }

  /// Clean up resources
  @override
  void dispose() {
    // Cancel all stream subscriptions
    for (final subscription in _subscriptions) {
      subscription.cancel();
    }
    _subscriptions.clear();

    // Clear caches
    _conversationCache.clear();

    // Reset state
    _client = null;
    _api = null;
    _currentUserAddress = null;
    _isInitialized = false;
    _isInitializing = false;
    _initializationError = null;

    super.dispose();
  }
}

/// Wallet Signer implementation for XMTP compatibility
class _WalletSigner implements xmtp.Signer {
  final EthPrivateKey _privateKey;

  _WalletSigner(this._privateKey);

  @override
  EthereumAddress get address => _privateKey.address;

  @override
  Future<Uint8List> Function(String) get signPersonalMessage =>
      (message) async => _privateKey.signPersonalMessageToUint8List(
        Uint8List.fromList(message.codeUnits),
      );
}
