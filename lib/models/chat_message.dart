/// Model class representing a chat message in the XMTP conversation
class ChatMessage {
  final String id;
  final String content;
  final String senderAddress;
  final DateTime sentAt;
  final bool isFromCurrentUser;

  const ChatMessage({
    required this.id,
    required this.content,
    required this.senderAddress,
    required this.sentAt,
    required this.isFromCurrentUser,
  });

  /// Create a ChatMessage from an XMTP message
  factory ChatMessage.fromXmtpMessage({
    required String id,
    required String content,
    required String senderAddress,
    required DateTime sentAt,
    required String currentUserAddress,
  }) {
    return ChatMessage(
      id: id,
      content: content,
      senderAddress: senderAddress,
      sentAt: sentAt,
      isFromCurrentUser: senderAddress.toLowerCase() == currentUserAddress.toLowerCase(),
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ChatMessage &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'ChatMessage{id: $id, content: $content, senderAddress: $senderAddress, sentAt: $sentAt, isFromCurrentUser: $isFromCurrentUser}';
  }
}
